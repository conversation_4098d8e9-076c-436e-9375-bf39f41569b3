// Compiler chuyển AST thành bytecode cho VM
import 'ast.dart';
import 'instruction.dart';
import 'opcode.dart' as opcodes;
import 'bytecode.dart';
import 'type_annotation.dart';

class Compiler {
  final List<Instruction> _instructions = [];
  bool _verbose = false;

  set verbose(bool value) => _verbose = value;

  Bytecode compile(ASTNode node) {
    _instructions.clear();
    _compileNode(node);
    return getBytecode();
  }

  void _compileNode(ASTNode node) {
    switch (node) {
      case Program():
        for (final stmt in node.body) {
          _compileNode(stmt);
        }

        break;
      case NumberLiteral():
        emit(opcodes.LoadConstOp(node.value));
        break;
      case StringLiteral():
        if (_verbose) {
          print('[STRING LIT]: ${node.value}');
        }
        emit(opcodes.LoadConstOp(node.value));
        break;
      case Identifier():
        emit(opcodes.LoadOp(node.name));
        break;
      case BinaryExpression():
        _compileNode(node.left);
        _compileNode(node.right);
        final newOp = switch (node.operator) {
          '+' => opcodes.AddOp(),
          '-' => opcodes.SubOp(),
          '*' => opcodes.MulOp(),
          '/' => opcodes.DivOp(),
          '%' => opcodes.ModOp(),
          '<' => opcodes.LessOp(),
          '>' => opcodes.GreaterOp(),
          '==' => opcodes.EqOp(),
          '!=' => opcodes.NeqOp(),
          '<=' => opcodes.LeqOp(),
          '>=' => opcodes.GeqOp(),
          _ => throw Exception('Unsupported operator: ${node.operator}'),
        };
        emit(newOp);
        break;
      case VariableDeclaration():
        for (var i = 0; i < node.names.length; i++) {
          _compileNode(node.values[i]);
          emit(opcodes.StoreOp(node.names[i]));
        }
        break;
      case PrintStatement():
        if (_verbose) {
          print('[PRINT COMPILING]: ${node.value}');
        }

        _compileNode(node.value);
        emit(opcodes.PrintOp());
        break;
      case ReturnStatement():
        _compileNode(node.value);
        emit(opcodes.ReturnOp());
        break;
      case ExpressionStatement():
        _compileNode(node.expression);
        break;
      case CallExpression():
        for (var arg in node.arguments) _compileNode(arg);
        emit(opcodes.CallOp(node.name, node.arguments.length));
        break;
      case FunctionDeclaration():
        final innerCompiler = Compiler();
        for (var i = 0; i < node.params.length; i++) {
          // Giả lập biến argX cho các param
          final param = node.params[i];
          innerCompiler.emit(opcodes.LoadOp('arg$i'));
          innerCompiler.emit(opcodes.StoreOp(param.name.name));
        }
        for (var stmt in node.body) {
          innerCompiler._compileNode(stmt);
        }
        innerCompiler.emit(opcodes.ReturnOp());
        if (_verbose) print('[DEBUG FUNC DECL]: ${node.name}');

        final funcCode = resolveLabels(Bytecode(innerCompiler._instructions));
        emit(opcodes.LoadConstOp(funcCode));
        emit(opcodes.StoreOp(node.name.name));
        break;
      case IfStatement():
        _compileNode(node.condition);
        final elseLabel = 'else_${node.hashCode}';
        final endLabel = 'endif_${node.hashCode}';
        emit(opcodes.JumpIfFalseOp(elseLabel));
        for (var stmt in node.thenBranch) _compileNode(stmt);
        emit(opcodes.JumpOp(endLabel));
        emit(opcodes.LabelOp(elseLabel));
        if (node.elseBranch != null) {
          for (var stmt in node.elseBranch!) _compileNode(stmt);
        }
        emit(opcodes.LabelOp(endLabel));
        break;
      case BooleanLiteral():
        emit(opcodes.LoadConstOp(node.value));
        break;
      case WhileStatement():
        final start = 'start_${node.hashCode}';
        final end = 'end_${node.hashCode}';
        emit(opcodes.LabelOp(start));
        _compileNode(node.condition);
        emit(opcodes.JumpIfFalseOp(end));
        for (var stmt in node.body) _compileNode(stmt);
        emit(opcodes.JumpOp(start));
        emit(opcodes.LabelOp(end));
        break;
      case ForStatement():
        _compileNode(node.start);
        emit(opcodes.StoreOp(node.iterator));
        final loopStart = 'for_start_${node.hashCode}';
        final loopEnd = 'for_end_${node.hashCode}';
        emit(opcodes.LabelOp(loopStart));
        emit(opcodes.LoadOp(node.iterator));
        _compileNode(node.end);
        emit(opcodes.GreaterOp());
        emit(opcodes.JumpIfTrueOp(loopEnd));
        for (var stmt in node.body) _compileNode(stmt);
        emit(opcodes.LoadOp(node.iterator));
        emit(opcodes.LoadConstOp(1));
        emit(opcodes.AddOp());
        emit(opcodes.StoreOp(node.iterator));
        emit(opcodes.JumpOp(loopStart));
        emit(opcodes.LabelOp(loopEnd));
        break;
      case AssignmentExpression():
        if (node.target is Identifier) {
          _compileNode(node.value);
          final identifier = node.target as Identifier;
          emit(opcodes.StoreOp(identifier.name));
        } else if (node.target is FieldAccess) {
          final fa = node.target as FieldAccess;
          if (fa.object is ThisExpression) {
            emit(opcodes.LoadThisDupOp()); // thay vì LOAD_THIS
          } else {
            _compileNode(fa.object); // Push object trước (ví dụ: `this`)
          }
          _compileNode(node.value); // Push value
          emit(opcodes.SetFieldOp(fa.field)); // SET_FIELD object.field = value
        } else {
          throw Exception('Invalid assignment target: ${node.target}');
        }
        break;

      case StructDeclaration():
        // Check if this is a generic struct
        if (node.name.contains('<') || _hasGenericFields(node.fields)) {
          emit(opcodes.DefineGenericStructOp(
            _extractBaseName(node.name),
            _extractTypeParameters(node.name),
            node.fields.map((k, v) => MapEntry(k, v.toString())),
            parameterBounds: _extractParameterBounds(node.fields),
          ));
        } else {
          emit(opcodes.DefineStructOp(
            node.name,
            node.fields.map((k, v) => MapEntry(k, v.toString())),
          ));
        }
        break;

      case InterfaceDeclaration():
        emit(opcodes.DefineInterfaceOp(
          node.name,
          node.methods
              .map(
                (m) => {
                  'name': m.name,
                  'params':
                      m.paramTypes
                          .map(
                            (p) => {
                              'name': p.name,
                              'type': p.type.toString(),
                            },
                          )
                          .toList(),
                  'returnType': m.returnType.toString(),
                },
              )
              .toList(),
        ));
        break;

      case TraitImplementation():
        final staticMethods = <String, List<Instruction>>{};
        final instanceMethods = <String, List<Instruction>>{};

        for (final method in node.methods) {
          final inner = Compiler()..verbose = _verbose;
          // ánh xạ argX → param.name
          for (var i = 0; i < method.params.length; i++) {
            final param = method.params[i];
            inner.emit(opcodes.LoadOp('arg$i'));
            inner.emit(opcodes.StoreOp(param.name.name));
          }
          for (final stmt in method.body) inner._compileNode(stmt);
          inner.emit(opcodes.ReturnOp());
          final compiled = resolveLabels(Bytecode(inner._instructions));

          if (method.isStatic) {
            staticMethods[method.name.name] = compiled.instructions;
          } else {
            instanceMethods[method.name.name] = compiled.instructions;
          }
        }

        emit(opcodes.ImplTraitOp(
          node.structName,
          node.traitName,
          instanceMethods.cast<String, dynamic>(),
          staticMethods.cast<String, dynamic>(),
        ));
        break;

      case StructLiteral():
        if (_verbose) {
          print('[STRUCT LITERAL]: ${node.name} with fields: ${node.fields.keys}');
        }

        // Check if this is a generic struct instantiation
        if (node.name.contains('<')) {
          final baseName = _extractBaseName(node.name);
          final typeArgs = _parseTypeArguments(node.name);

          // Compile field values (they will be on stack)
          for (final field in node.fields.entries) {
            _compileNode(field.value);
          }

          emit(opcodes.InstantiateGenericOp(
            baseName,
            typeArgs,
            node.fields.keys.toList(),
          ));
        } else {
          // Compile field values first
          for (final field in node.fields.entries) {
            _compileNode(field.value); // push value trước
          }
          emit(opcodes.MakeStructOp(node.name, node.fields.keys.toList()));
        }
        break;

      case FieldAccess():
        _compileNode(node.object); // push struct instance
        emit(opcodes.GetFieldOp(node.field)); // lấy field từ struct
        break;

      case FieldAssign():
        _compileNode(node.object); // push instance
        _compileNode(node.value); // push value
        emit(opcodes.SetFieldOp(node.field));
        break;

      case StaticMethodCall():
        for (var arg in node.arguments) _compileNode(arg);
        emit(opcodes.StaticCallOp(
          node.structName,
          node.method,
          node.arguments.length,
        ));
        break;

      case MethodCall():
        if (_verbose) {
          print('[METHOD CALL]: ${node.method} with args: ${node.arguments}');
        }
        _compileNode(node.receiver); // Push receiver trước
        for (var arg in node.arguments) _compileNode(arg); // Push args sau
        emit(opcodes.MethodCallOp(node.method, node.arguments.length));
        break;

      case ThisExpression():
        emit(opcodes.LoadThisOp());
        break;

      case UnaryExpression():
        _compileNode(node.operand);
        final newOp = switch (node.operator) {
          '!' => opcodes.NotOp(),
          '-' => opcodes.NegOp(),
          '+' => opcodes.PosOp(),
          _ => throw Exception('Unsupported unary operator: ${node.operator}'),
        };
        emit(newOp);
        break;

      case EnumDeclaration():
        // Convert enum variants to Map<String, List<String>>
        final variants = <String, List<String>>{};
        for (final variant in node.variants) {
          if (variant.fields != null) {
            variants[variant.name] = variant.fields!.map((f) => f.toString()).toList();
          } else {
            variants[variant.name] = [];
          }
        }
        emit(opcodes.DefineEnumOp(node.name, variants));
        break;

      case Comment():
        // Comments are ignored during compilation - they don't generate any bytecode
        if (_verbose) {
          print('[COMMENT]: ${node.text}');
        }
        break;

      default:
        throw Exception('Unhandled AST node type: ${node.runtimeType}. This node type has not been migrated to the new OpCode system yet.');
    }
  }

  // Emit method for type-safe OpCodes
  void emit(opcodes.OpCode op) {
    _instructions.add(Instruction(op));
  }

  Bytecode getBytecode() => resolveLabels(Bytecode(_instructions));

  // Helper methods for generic support
  bool _hasGenericFields(Map<String, TypeAnnotation> fields) {
    return fields.values.any((type) => _isGenericType(type));
  }

  bool _isGenericType(TypeAnnotation type) {
    if (type is GenericType) return true;
    if (type is TraitBoundType) return true;
    if (type is OptionalType) return _isGenericType(type.base);
    return false;
  }

  String _extractBaseName(String name) {
    final index = name.indexOf('<');
    return index == -1 ? name : name.substring(0, index);
  }

  List<String> _extractTypeParameters(String name) {
    final match = RegExp(r'<([^>]+)>').firstMatch(name);
    if (match == null) return [];

    return match.group(1)!
        .split(',')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
  }

  Map<String, List<String>>? _extractParameterBounds(Map<String, TypeAnnotation> fields) {
    final bounds = <String, List<String>>{};

    for (final field in fields.values) {
      if (field is TraitBoundType) {
        bounds[field.name] = field.traitBounds;
      }
    }

    return bounds.isEmpty ? null : bounds;
  }

  Map<String, String> _parseTypeArguments(String name) {
    final match = RegExp(r'<([^>]+)>').firstMatch(name);
    if (match == null) return {};

    final typeArgsStr = match.group(1)!;
    final typeArgs = <String, String>{};

    // Simple parsing for now - assumes format like "T=int, U=string"
    // In a real implementation, this would be more sophisticated
    final parts = typeArgsStr.split(',');
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();
      if (part.contains('=')) {
        final keyValue = part.split('=');
        typeArgs[keyValue[0].trim()] = keyValue[1].trim();
      } else {
        // Assume positional mapping T, U, V...
        final paramNames = ['T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        if (i < paramNames.length) {
          typeArgs[paramNames[i]] = part;
        }
      }
    }

    return typeArgs;
  }
}

// Simplified resolveLabels for new OpCode system
Bytecode resolveLabels(Bytecode original) {
  final List<Instruction> result = [];
  final Map<String, int> labelPositions = {};
  int realIndex = 0;

  // === Bước 1: Ghi nhận vị trí labels ===
  for (int i = 0; i < original.instructions.length; i++) {
    final instr = original.instructions[i];
    if (instr.op is opcodes.LabelOp) {
      final labelOp = instr.op as opcodes.LabelOp;
      labelPositions[labelOp.name] = realIndex;
    } else {
      realIndex++;
    }
  }

  // === Bước 2: Tạo danh sách instruction mới đã thay thế label ===
  for (final instr in original.instructions) {
    final op = instr.op;

    if (op is opcodes.LabelOp) continue; // Bỏ qua LABEL

    if (op is opcodes.JumpOp) {
      final index = labelPositions[op.target];
      if (index == null) {
        throw Exception('Unknown label: ${op.target}');
      }
      result.add(Instruction(opcodes.JumpOp(index.toString())));
    } else if (op is opcodes.JumpIfTrueOp) {
      final index = labelPositions[op.target];
      if (index == null) {
        throw Exception('Unknown label: ${op.target}');
      }
      result.add(Instruction(opcodes.JumpIfTrueOp(index.toString())));
    } else if (op is opcodes.JumpIfFalseOp) {
      final index = labelPositions[op.target];
      if (index == null) {
        throw Exception('Unknown label: ${op.target}');
      }
      result.add(Instruction(opcodes.JumpIfFalseOp(index.toString())));
    } else {
      result.add(instr); // Giữ nguyên
    }
  }

  return Bytecode(result);
}
